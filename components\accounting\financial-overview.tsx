"use client"

import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import type { DateRange } from "react-day-picker"
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

interface FinancialOverviewProps {
  dateRange?: DateRange
}

export function FinancialOverview({ dateRange }: FinancialOverviewProps) {
  // Mock financial data
  const revenueData = [
    { date: "Mar 01", revenue: 1200, expenses: 800, profit: 400 },
    { date: "Mar 05", revenue: 1800, expenses: 1000, profit: 800 },
    { date: "Mar 10", revenue: 1400, expenses: 900, profit: 500 },
    { date: "Mar 15", revenue: 2200, expenses: 1100, profit: 1100 },
    { date: "Mar 20", revenue: 1900, expenses: 950, profit: 950 },
    { date: "Mar 25", revenue: 2400, expenses: 1200, profit: 1200 },
    { date: "Mar 30", revenue: 2800, expenses: 1300, profit: 1500 },
  ]

  return (
    <div className="grid gap-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold"><CurrencyDisplay amount={13700} /></div>
            <p className="text-xs text-muted-foreground">+15% from last month</p>
            <div className="mt-4 h-[80px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={revenueData}>
                  <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} dot={false} />
                  <Tooltip />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold"><CurrencyDisplay amount={7250} /></div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
            <div className="mt-4 h-[80px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={revenueData}>
                  <Line type="monotone" dataKey="expenses" stroke="#82ca9d" strokeWidth={2} dot={false} />
                  <Tooltip />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold"><CurrencyDisplay amount={6450} /></div>
            <p className="text-xs text-muted-foreground">+25% from last month</p>
            <div className="mt-4 h-[80px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={revenueData}>
                  <Line type="monotone" dataKey="profit" stroke="#ffc658" strokeWidth={2} dot={false} />
                  <Tooltip />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Profit Margin</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">47.1%</div>
            <p className="text-xs text-muted-foreground">+8% from last month</p>
            <div className="mt-4 h-[80px]">
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div className="h-full bg-primary" style={{ width: "47.1%" }}></div>
              </div>
              <div className="flex justify-between text-xs text-muted-foreground mt-2">
                <span>0%</span>
                <span>50%</span>
                <span>100%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Financial Trends</CardTitle>
          <CardDescription>Revenue, expenses, and profit over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={revenueData}>
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="revenue" name="Revenue" stroke="#8884d8" strokeWidth={2} />
                <Line type="monotone" dataKey="expenses" name="Expenses" stroke="#82ca9d" strokeWidth={2} />
                <Line type="monotone" dataKey="profit" name="Profit" stroke="#ffc658" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Breakdown</CardTitle>
            <CardDescription>Revenue by category</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="services">
              <TabsList className="mb-4">
                <TabsTrigger value="services">Services</TabsTrigger>
                <TabsTrigger value="products">Products</TabsTrigger>
                <TabsTrigger value="locations">Locations</TabsTrigger>
              </TabsList>

              <TabsContent value="services">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Haircut & Style</p>
                      <p className="text-xs text-muted-foreground">45 services</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={3375} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "25%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Color & Highlights</p>
                      <p className="text-xs text-muted-foreground">22 services</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={3300} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "24%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Blowout</p>
                      <p className="text-xs text-muted-foreground">18 services</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={1170} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "8.5%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Other Services</p>
                      <p className="text-xs text-muted-foreground">49 services</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={2705} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "19.7%" }}></div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="products">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Shampoo & Conditioner</p>
                      <p className="text-xs text-muted-foreground">32 units</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={1280} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "40%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Styling Products</p>
                      <p className="text-xs text-muted-foreground">28 units</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={980} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "30.6%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Hair Tools</p>
                      <p className="text-xs text-muted-foreground">8 units</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={560} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "17.5%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Other Products</p>
                      <p className="text-xs text-muted-foreground">15 units</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={380} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "11.9%" }}></div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="locations">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Downtown</p>
                      <p className="text-xs text-muted-foreground">68 transactions</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={5850} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "42.7%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Westside</p>
                      <p className="text-xs text-muted-foreground">52 transactions</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={4680} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "34.2%" }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Northside</p>
                      <p className="text-xs text-muted-foreground">38 transactions</p>
                    </div>
                    <p className="font-medium"><CurrencyDisplay amount={3170} /></p>
                  </div>
                  <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "23.1%" }}></div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Expense Breakdown</CardTitle>
            <CardDescription>Expenses by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Payroll</p>
                  <p className="text-xs text-muted-foreground">Staff salaries and commissions</p>
                </div>
                <p className="font-medium"><CurrencyDisplay amount={4200} /></p>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div className="h-full bg-destructive" style={{ width: "58%" }}></div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Inventory</p>
                  <p className="text-xs text-muted-foreground">Products and supplies</p>
                </div>
                <p className="font-medium"><CurrencyDisplay amount={1850} /></p>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div className="h-full bg-destructive" style={{ width: "25.5%" }}></div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Rent & Utilities</p>
                  <p className="text-xs text-muted-foreground">Facility costs</p>
                </div>
                <p className="font-medium"><CurrencyDisplay amount={950} /></p>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div className="h-full bg-destructive" style={{ width: "13.1%" }}></div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Marketing</p>
                  <p className="text-xs text-muted-foreground">Advertising and promotions</p>
                </div>
                <p className="font-medium"><CurrencyDisplay amount={250} /></p>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div className="h-full bg-destructive" style={{ width: "3.4%" }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

