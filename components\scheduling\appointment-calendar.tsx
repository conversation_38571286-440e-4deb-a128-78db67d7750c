"use client"

import { useState, useMemo } from "react"
import { useAuth } from "@/lib/auth-provider"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { addDays, format, isSameDay, parseISO, startOfWeek } from "date-fns"
import { mockAppointments } from "@/lib/mock-data"
import { useStaff } from "@/lib/staff-provider"

interface AppointmentCalendarProps {
  onDateSelect?: (date: Date) => void
  onAppointmentClick?: (appointment: any) => void
  onCreateAppointment?: (date: Date) => void
  selectedDate?: Date
  viewMode?: "day" | "week" | "month"
}

export function AppointmentCalendar({
  onDateSelect,
  onAppointmentClick,
  onCreateAppointment,
  selectedDate = new Date(),
  viewMode: initialViewMode = "day",
}: AppointmentCalendarProps) {
  const { currentLocation } = useAuth()
  const [date, setDate] = useState<Date>(selectedDate)
  const [viewMode, setViewMode] = useState<"day" | "week" | "month">(initialViewMode)
  const [staffFilter, setStaffFilter] = useState<string>("all")

  // Filter appointments based on location, date, and staff
  const filteredAppointments = useMemo(() => {
    return mockAppointments.filter((appointment) => {
      // Filter by location
      if (currentLocation !== "all" && appointment.location !== currentLocation) {
        return false
      }

      // Filter by date based on view mode
      const appointmentDate = parseISO(appointment.date)
      if (viewMode === "day") {
        if (!isSameDay(appointmentDate, date)) {
          return false
        }
      } else if (viewMode === "week") {
        const weekStart = startOfWeek(date)
        const weekEnd = addDays(weekStart, 6)
        if (appointmentDate < weekStart || appointmentDate > weekEnd) {
          return false
        }
      }
      // Month view is handled by the Calendar component

      // Filter by staff if needed
      if (staffFilter !== "all" && appointment.staffId !== staffFilter) {
        return false
      }

      // Get the staff member for this appointment
      const staffMember = staffList.find(s => s.id === appointment.staffId);

      // Only show appointments for staff members who are assigned to the selected location
      // This ensures staff members only show appointments at their assigned locations
      const isStaffAssignedToLocation =
        currentLocation === "all" ||
        (staffMember && staffMember.locations.includes(currentLocation)) ||
        (currentLocation === "home" && staffMember && staffMember.homeService);

      if (!isStaffAssignedToLocation) {
        return false;
      }

      return true
    })
  }, [currentLocation, date, viewMode, staffFilter])

  // Use the staff provider to get staff data
  const { staff, getStaffByLocation } = useStaff();

  // Get staff for the current location
  const availableStaff = getStaffByLocation(currentLocation);

  // Use staff list for filtering appointments
  const staffList = staff;

  // Debug log to check staff filtering
  console.log("AppointmentCalendar - Current Location:", currentLocation);
  console.log("AppointmentCalendar - Available Staff:", availableStaff.map(s => ({
    name: s.name,
    locations: s.locations,
    assignedToCurrentLocation: s.locations.includes(currentLocation) ||
                              (currentLocation === "home" && s.homeService) ||
                              currentLocation === "all"
  })));

  // Handle date change
  const handleDateChange = (newDate: Date | undefined) => {
    if (newDate) {
      setDate(newDate)
      if (onDateSelect) {
        onDateSelect(newDate)
      }
    }
  }

  // Create time slots for day view
  const timeSlots = []
  for (let i = 9; i < 19; i++) {
    timeSlots.push(`${i}:00`)
    timeSlots.push(`${i}:30`)
  }

  // Group appointments by time slot for day view
  const appointmentsByTime = useMemo(() => {
    const grouped: Record<string, any[]> = {}

    timeSlots.forEach((time) => {
      grouped[time] = []
    })

    filteredAppointments.forEach((appointment) => {
      const appointmentDate = parseISO(appointment.date)
      const hour = appointmentDate.getHours()
      const minute = appointmentDate.getMinutes()
      const timeKey = `${hour}:${minute === 0 ? "00" : minute}`

      if (grouped[timeKey]) {
        grouped[timeKey].push(appointment)
      }
    })

    return grouped
  }, [filteredAppointments])

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <CardTitle className="text-xl">Appointment Calendar</CardTitle>
          <div className="flex items-center gap-2">
            <Tabs defaultValue={viewMode} onValueChange={(v) => setViewMode(v as any)}>
              <TabsList>
                <TabsTrigger value="day">Day</TabsTrigger>
                <TabsTrigger value="week">Week</TabsTrigger>
                <TabsTrigger value="month">Month</TabsTrigger>
              </TabsList>
            </Tabs>

            <Tabs defaultValue={staffFilter} onValueChange={setStaffFilter}>
              <TabsList>
                <TabsTrigger value="all">All Staff</TabsTrigger>
                {availableStaff.map((staff) => (
                  <TabsTrigger key={staff.id} value={staff.id}>
                    {staff.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-[300px_1fr] gap-4">
          <div>
            <Calendar mode="single" selected={date} onSelect={handleDateChange} className="rounded-md border" />
            <div className="mt-4">
              <Button className="w-full" onClick={() => onCreateAppointment && onCreateAppointment(date)}>
                New Appointment
              </Button>
            </div>
          </div>

          <div className="overflow-auto">
            {viewMode === "day" && (
              <div className="space-y-2">
                <h3 className="font-medium text-lg">{format(date, "EEEE, MMMM d, yyyy")}</h3>
                <div className="border rounded-md">
                  {timeSlots.map((timeSlot) => {
                    const [hour, minute] = timeSlot.split(":")
                    const appointments = appointmentsByTime[timeSlot] || []
                    const hasAppointments = appointments.length > 0

                    return (
                      <div
                        key={timeSlot}
                        className={cn("flex flex-col border-b last:border-b-0", hasAppointments ? "min-h-20" : "h-12")}
                      >
                        <div className="flex">
                          <div className="w-20 p-2 border-r flex-shrink-0 font-medium text-sm text-muted-foreground">
                            {`${hour}:${minute}`}
                          </div>
                          <div className="flex-1 p-1">
                            {appointments.map((appointment) => (
                              <div
                                key={appointment.id}
                                className={cn(
                                  "p-2 rounded-md mb-1 cursor-pointer",
                                  appointment.status === "confirmed"
                                    ? "bg-blue-100 border-blue-300 dark:bg-blue-950 dark:border-blue-800"
                                    : appointment.status === "completed"
                                      ? "bg-green-100 border-green-300 dark:bg-green-950 dark:border-green-800"
                                      : appointment.status === "cancelled"
                                        ? "bg-red-100 border-red-300 dark:bg-red-950 dark:border-red-800"
                                        : "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-700",
                                  "border",
                                )}
                                onClick={() => onAppointmentClick && onAppointmentClick(appointment)}
                              >
                                <div className="flex justify-between">
                                  <span className="font-medium">{appointment.clientName}</span>
                                  <span className="text-sm">{format(parseISO(appointment.date), "h:mm a")}</span>
                                </div>
                                <div className="flex gap-2 mt-1 text-sm">
                                  <span>{appointment.service}</span>
                                  <span className="text-muted-foreground">({appointment.duration} min)</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {viewMode === "week" && (
              <div className="space-y-2">
                <h3 className="font-medium text-lg">Week of {format(startOfWeek(date), "MMMM d, yyyy")}</h3>
                <div className="overflow-x-auto">
                  <div className="min-w-[800px] border rounded-md p-4">
                    {/* Week view would go here - for now showing a simple list */}
                    <div className="space-y-4">
                      {filteredAppointments.map((appointment) => (
                        <div
                          key={appointment.id}
                          className={cn(
                            "p-3 rounded-md cursor-pointer",
                            appointment.status === "confirmed"
                              ? "bg-blue-100 border-blue-300 dark:bg-blue-950 dark:border-blue-800"
                              : appointment.status === "completed"
                                ? "bg-green-100 border-green-300 dark:bg-green-950 dark:border-green-800"
                                : appointment.status === "cancelled"
                                  ? "bg-red-100 border-red-300 dark:bg-red-950 dark:border-red-800"
                                  : "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-700",
                            "border",
                          )}
                          onClick={() => onAppointmentClick && onAppointmentClick(appointment)}
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="font-medium text-base">{appointment.clientName}</div>
                              <div className="text-sm text-muted-foreground">
                                {format(parseISO(appointment.date), "EEEE, MMMM d")} at{" "}
                                {format(parseISO(appointment.date), "h:mm a")}
                              </div>
                            </div>
                            <div className="text-sm font-medium">
                              {appointment.service} ({appointment.duration} min)
                            </div>
                          </div>
                        </div>
                      ))}

                      {filteredAppointments.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          No appointments found for this week.
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {viewMode === "month" && (
              <div className="space-y-2">
                <h3 className="font-medium text-lg">{format(date, "MMMM yyyy")}</h3>
                <div className="overflow-x-auto">
                  <div className="min-w-[800px] border rounded-md p-4">
                    {/* Month view would go here - for now showing a simple list */}
                    <div className="space-y-4">
                      {filteredAppointments.map((appointment) => (
                        <div
                          key={appointment.id}
                          className={cn(
                            "p-3 rounded-md cursor-pointer",
                            appointment.status === "confirmed"
                              ? "bg-blue-100 border-blue-300 dark:bg-blue-950 dark:border-blue-800"
                              : appointment.status === "completed"
                                ? "bg-green-100 border-green-300 dark:bg-green-950 dark:border-green-800"
                                : appointment.status === "cancelled"
                                  ? "bg-red-100 border-red-300 dark:bg-red-950 dark:border-red-800"
                                  : "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-700",
                            "border",
                          )}
                          onClick={() => onAppointmentClick && onAppointmentClick(appointment)}
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="font-medium text-base">{appointment.clientName}</div>
                              <div className="text-sm text-muted-foreground">
                                {format(parseISO(appointment.date), "EEEE, MMMM d")} at{" "}
                                {format(parseISO(appointment.date), "h:mm a")}
                              </div>
                            </div>
                            <div className="text-sm font-medium">
                              {appointment.service} ({appointment.duration} min)
                            </div>
                          </div>
                        </div>
                      ))}

                      {filteredAppointments.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          No appointments found for this month.
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

