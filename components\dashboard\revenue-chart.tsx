"use client"

import { useTheme } from "next-themes"
import { useAuth } from "@/lib/auth-provider"
import { useCurrency } from "@/lib/currency-provider"
import { format, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval, isSameDay, isSameMonth } from "date-fns"
import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from "recharts"

interface RevenueChartProps {
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export function RevenueChart({ dateRange }: RevenueChartProps) {
  const { theme } = useTheme()
  const { currentLocation } = useAuth()
  const { currency, formatCurrency } = useCurrency()

  // Generate data based on date range
  const generateChartData = () => {
    if (!dateRange?.from || !dateRange?.to) {
      return defaultData
    }

    const { from, to } = dateRange
    const daysDiff = Math.ceil((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24))

    // For different time periods, use different grouping
    if (daysDiff <= 1) {
      // Single day - show hourly data
      const hours = Array.from({ length: 12 }, (_, i) => i + 9) // 9am to 8pm
      return hours.map(hour => {
        const cardRevenue = Math.floor(Math.random() * 300) + 100
        const cashRevenue = Math.floor(Math.random() * 200) + 50
        return {
          name: `${hour > 12 ? hour - 12 : hour}${hour >= 12 ? 'pm' : 'am'}`,
          card: cardRevenue,
          cash: cashRevenue,
          total: cardRevenue + cashRevenue
        }
      })
    } else if (daysDiff <= 31) {
      // Up to a month - show daily data
      return eachDayOfInterval({ start: from, end: to }).map(date => {
        const cardRevenue = Math.floor(Math.random() * 1000) + 500
        const cashRevenue = Math.floor(Math.random() * 500) + 200
        return {
          name: format(date, "MMM d"),
          date,
          card: cardRevenue,
          cash: cashRevenue,
          total: cardRevenue + cashRevenue
        }
      })
    } else if (daysDiff <= 90) {
      // Up to 3 months - show weekly data
      return eachWeekOfInterval({ start: from, end: to }, { weekStartsOn: 1 }).map(date => {
        const cardRevenue = Math.floor(Math.random() * 5000) + 2000
        const cashRevenue = Math.floor(Math.random() * 2500) + 1000
        return {
          name: `Week of ${format(date, "MMM d")}`,
          date,
          card: cardRevenue,
          cash: cashRevenue,
          total: cardRevenue + cashRevenue
        }
      })
    } else {
      // More than 3 months - show monthly data
      return eachMonthOfInterval({ start: from, end: to }).map(date => {
        const cardRevenue = Math.floor(Math.random() * 15000) + 8000
        const cashRevenue = Math.floor(Math.random() * 7500) + 3000
        return {
          name: format(date, "MMM yyyy"),
          date,
          card: cardRevenue,
          cash: cashRevenue,
          total: cardRevenue + cashRevenue
        }
      })
    }
  }

  // Default monthly data
  const defaultData = [
    { name: "Jan", card: 1500, cash: 1000, total: 2500 },
    { name: "Feb", card: 1800, cash: 1200, total: 3000 },
    { name: "Mar", card: 1700, cash: 1100, total: 2800 },
    { name: "Apr", card: 2000, cash: 1200, total: 3200 },
    { name: "May", card: 2500, cash: 1500, total: 4000 },
    { name: "Jun", card: 2300, cash: 1500, total: 3800 },
    { name: "Jul", card: 2600, cash: 1600, total: 4200 },
    { name: "Aug", card: 2800, cash: 1700, total: 4500 },
    { name: "Sep", card: 3000, cash: 1800, total: 4800 },
    { name: "Oct", card: 3200, cash: 1800, total: 5000 },
    { name: "Nov", card: 2900, cash: 1800, total: 4700 },
    { name: "Dec", card: 3300, cash: 1900, total: 5200 },
  ]

  const data = generateChartData()

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data}>
          <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${currency.symbol}${value}`}
          />
          <Tooltip
            formatter={(value, name) => {
              const formattedName = name === 'card' ? 'Card' : name === 'cash' ? 'Cash' : 'Total'
              return [`${currency.symbol}${value}`, formattedName]
            }}
            contentStyle={{
              backgroundColor: theme === "dark" ? "#1f2937" : "#ffffff",
              borderColor: theme === "dark" ? "#374151" : "#e5e7eb",
            }}
          />
          <Legend />
          <Bar dataKey="card" name="Card" fill="#8884d8" radius={[4, 4, 0, 0]} />
          <Bar dataKey="cash" name="Cash" fill="#82ca9d" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

