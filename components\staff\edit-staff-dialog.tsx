"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { useLocations } from "@/lib/location-provider"
import { StaffStorage } from "@/lib/staff-storage"

interface EditStaffDialogProps {
  staffId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onStaffUpdated?: (updatedStaff: any) => void
}

export function EditStaffDialog({ staffId, open, onOpenChange, onStaffUpdated }: EditStaffDialogProps) {
  const { user, currentLocation } = useAuth()
  const { toast } = useToast()
  const { locations: availableLocations, isHomeServiceEnabled } = useLocations()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    role: "",
    locations: [] as string[],
    status: "Active",
    homeService: false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch staff data when dialog opens
  useEffect(() => {
    if (open && staffId) {
      // Get staff data from storage
      const staff = StaffStorage.getStaffById(staffId)
      if (staff) {
        setFormData({
          name: staff.name,
          email: staff.email,
          phone: staff.phone,
          role: staff.role,
          locations: staff.locations,
          status: staff.status,
          homeService: staff.homeService || false
        })
      }
    }
  }, [open, staffId])

  const handleLocationChange = (location: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        locations: [...prev.locations, location]
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        locations: prev.locations.filter(loc => loc !== location)
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Get the existing staff data to preserve fields we don't edit
      const existingStaff = StaffStorage.getStaffById(staffId)

      if (!existingStaff) {
        throw new Error(`Staff with ID ${staffId} not found`)
      }

      // Create updated staff object
      const updatedStaff = {
        ...existingStaff,
        id: staffId,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        role: formData.role,
        locations: formData.locations,
        status: formData.status,
        homeService: formData.homeService,
        // Update avatar if name changed
        avatar: formData.name.split(' ').map(n => n[0]).join(''),
      }

      // Save to storage
      StaffStorage.updateStaff(updatedStaff)

      console.log("Staff updated:", updatedStaff)

      // Dispatch event to notify components about staff update
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('staff-updated'))
      }

      toast({
        title: "Staff updated",
        description: `${formData.name}'s information has been updated successfully.`,
      })

      if (onStaffUpdated) {
        onStaffUpdated(updatedStaff)
      }

      onOpenChange(false)
    } catch (error) {
      console.error("Failed to update staff:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update staff member. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Staff Member</DialogTitle>
            <DialogDescription>Update staff member information.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                Phone
              </Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Role
              </Label>
              <Select
                value={formData.role}
                onValueChange={(value) => setFormData({...formData, role: value})}
              >
                <SelectTrigger id="role" className="col-span-3">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {user?.role === "super_admin" && <SelectItem value="super_admin">Super Admin</SelectItem>}
                  {(user?.role === "super_admin" || user?.role === "org_admin") && (
                    <SelectItem value="org_admin">Organization Admin</SelectItem>
                  )}
                  {(user?.role === "super_admin" || user?.role === "org_admin") && (
                    <SelectItem value="location_manager">Location Manager</SelectItem>
                  )}
                  <SelectItem value="stylist">Stylist</SelectItem>
                  <SelectItem value="colorist">Colorist</SelectItem>
                  <SelectItem value="barber">Barber</SelectItem>
                  <SelectItem value="nail_technician">Nail Technician</SelectItem>
                  <SelectItem value="esthetician">Esthetician</SelectItem>
                  <SelectItem value="receptionist">Receptionist</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">Locations</Label>
              <div className="col-span-3 space-y-2">
                {/* Map through available locations from settings */}
                {availableLocations
                  .filter(location => location.status === "Active")
                  .map(location => (
                    <div key={location.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={location.id}
                        checked={formData.locations.includes(location.id)}
                        onCheckedChange={(checked) => handleLocationChange(location.id, checked === true)}
                      />
                      <Label htmlFor={location.id}>{location.name}</Label>
                    </div>
                  ))
                }

                {/* Add Home Service option if enabled */}
                {isHomeServiceEnabled && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="home"
                      checked={formData.locations.includes("home")}
                      onCheckedChange={(checked) => handleLocationChange("home", checked === true)}
                    />
                    <Label htmlFor="home">Home Service Location</Label>
                  </div>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData({...formData, status: value})}
              >
                <SelectTrigger id="status" className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                  <SelectItem value="On Leave">On Leave</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Home Service</Label>
              <div className="col-span-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="homeService"
                    checked={formData.homeService}
                    onCheckedChange={(checked) => setFormData({...formData, homeService: checked === true})}
                  />
                  <Label htmlFor="homeService">Available for home service</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" type="button" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
