import { NextResponse } from "next/server";
import { loyaltyData, pointHistory } from "@/lib/loyalty-data";

// Get loyalty data for a client
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get("clientId");

    if (!clientId) {
      return NextResponse.json({ error: "Client ID is required" }, { status: 400 });
    }

    const clientLoyalty = loyaltyData[clientId as keyof typeof loyaltyData];

    if (!clientLoyalty) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    const clientHistory = pointHistory[clientId as keyof typeof pointHistory] || [];

    return NextResponse.json({
      loyalty: clientLoyalty,
      history: clientHistory
    });
  } catch (error) {
    console.error("Error fetching loyalty data:", error);
    return NextResponse.json({ error: "Failed to fetch loyalty data" }, { status: 500 });
  }
}

// Redeem a reward
export async function POST(request: Request) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.clientId || !data.rewardId) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    const clientLoyalty = loyaltyData[data.clientId as keyof typeof loyaltyData];

    if (!clientLoyalty) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    // Find the reward
    const reward = clientLoyalty.availableRewards.find(r => r.id === data.rewardId);

    if (!reward) {
      return NextResponse.json({ error: "Reward not found" }, { status: 404 });
    }

    // Check if client has enough points
    if (clientLoyalty.points < reward.pointsCost) {
      return NextResponse.json({
        error: "Insufficient points",
        pointsNeeded: reward.pointsCost - clientLoyalty.points
      }, { status: 400 });
    }

    // Redeem the reward
    const redeemedReward = {
      id: `rr${clientLoyalty.redeemedRewards.length + 1}`,
      rewardId: reward.id,
      name: reward.name,
      redeemedAt: new Date().toISOString(),
      usedAt: null,
      pointsCost: reward.pointsCost
    };

    // Update client's loyalty data
    clientLoyalty.points -= reward.pointsCost;
    clientLoyalty.redeemedRewards.push(redeemedReward);

    // Add to point history
    const clientHistoryArray = pointHistory[data.clientId as keyof typeof pointHistory];
    if (clientHistoryArray) {
      clientHistoryArray.unshift({
        id: `ph${clientHistoryArray.length + 1}`,
        date: new Date().toISOString(),
        description: `Reward Redemption: ${reward.name}`,
        points: -reward.pointsCost,
        type: "redeemed"
      });
    }

    return NextResponse.json({
      success: true,
      redeemedReward,
      updatedPoints: clientLoyalty.points
    });
  } catch (error) {
    console.error("Error redeeming reward:", error);
    return NextResponse.json({ error: "Failed to redeem reward" }, { status: 500 });
  }
}

// Add points (from purchase, referral, etc.)
export async function PUT(request: Request) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.clientId || !data.points || !data.description) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    const clientLoyalty = loyaltyData[data.clientId as keyof typeof loyaltyData];

    if (!clientLoyalty) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    // Add points to client's balance
    clientLoyalty.points += data.points;
    clientLoyalty.totalPointsEarned += data.points;

    // Add to point history
    const clientHistoryArray = pointHistory[data.clientId as keyof typeof pointHistory];
    if (clientHistoryArray) {
      clientHistoryArray.unshift({
        id: `ph${clientHistoryArray.length + 1}`,
        date: new Date().toISOString(),
        description: data.description,
        points: data.points,
        type: "earned"
      });
    }

    // Check if client has reached a new tier
    let tierUpdated = false;
    if (clientLoyalty.tier === "Bronze" && clientLoyalty.totalPointsEarned >= 500) {
      clientLoyalty.tier = "Silver";
      clientLoyalty.nextTier = "Gold";
      clientLoyalty.pointsToNextTier = 1000 - clientLoyalty.totalPointsEarned;
      tierUpdated = true;
    } else if (clientLoyalty.tier === "Silver" && clientLoyalty.totalPointsEarned >= 1000) {
      clientLoyalty.tier = "Gold";
      clientLoyalty.nextTier = "Platinum";
      clientLoyalty.pointsToNextTier = 2000 - clientLoyalty.totalPointsEarned;
      tierUpdated = true;
    } else if (clientLoyalty.tier === "Gold" && clientLoyalty.totalPointsEarned >= 2000) {
      clientLoyalty.tier = "Platinum";
      clientLoyalty.nextTier = "Diamond";
      clientLoyalty.pointsToNextTier = 5000 - clientLoyalty.totalPointsEarned;
      tierUpdated = true;
    }

    return NextResponse.json({
      success: true,
      updatedPoints: clientLoyalty.points,
      tierUpdated,
      newTier: tierUpdated ? clientLoyalty.tier : null
    });
  } catch (error) {
    console.error("Error adding points:", error);
    return NextResponse.json({ error: "Failed to add points" }, { status: 500 });
  }
}
