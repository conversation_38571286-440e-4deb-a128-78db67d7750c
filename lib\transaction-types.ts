"use client"

import { z } from "zod"

/**
 * Enum for transaction sources
 */
export enum TransactionSource {
  POS = "pos",
  CALENDAR = "calendar",
  MANUAL = "manual",
  INVENTORY = "inventory",
  ONLINE = "online",
  SYSTEM = "system"
}

/**
 * Enum for transaction types
 */
export enum TransactionType {
  INCOME = "income",
  EXPENSE = "expense",
  REFUND = "refund",
  ADJUSTMENT = "adjustment"
}

/**
 * Enum for transaction status
 */
export enum TransactionStatus {
  COMPLETED = "completed",
  PENDING = "pending",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
  PARTIAL = "partial"
}

/**
 * Enum for payment methods
 */
export enum PaymentMethod {
  CREDIT_CARD = "credit_card",
  CASH = "cash",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHECK = "check",
  GIFT_CARD = "gift_card",
  LOYALTY_POINTS = "loyalty_points",
  OTHER = "other"
}

/**
 * Interface for transaction data
 */
export interface Transaction {
  id: string;
  date: Date | string;
  clientId?: string;
  clientName?: string;
  staffId?: string;
  staffName?: string;
  type: TransactionType;
  category: string;
  description: string;
  amount: number;
  paymentMethod: PaymentMethod;
  status: TransactionStatus;
  location: string;
  source: TransactionSource;
  reference?: {
    type: string;
    id: string;
  };
  metadata?: Record<string, any>;
  createdAt: Date | string;
  updatedAt: Date | string;
}

/**
 * Zod schema for transaction validation
 */
export const transactionSchema = z.object({
  id: z.string().optional(),
  date: z.union([z.date(), z.string().datetime()]),
  clientId: z.string().optional(),
  clientName: z.string().optional(),
  staffId: z.string().optional(),
  staffName: z.string().optional(),
  type: z.nativeEnum(TransactionType),
  category: z.string(),
  description: z.string().optional(),
  amount: z.number().positive(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  status: z.nativeEnum(TransactionStatus),
  location: z.string(),
  source: z.nativeEnum(TransactionSource),
  reference: z.object({
    type: z.string(),
    id: z.string()
  }).optional(),
  metadata: z.record(z.any()).optional(),
  createdAt: z.union([z.date(), z.string().datetime()]).optional(),
  updatedAt: z.union([z.date(), z.string().datetime()]).optional()
});

/**
 * Type for transaction creation
 */
export type TransactionCreate = z.infer<typeof transactionSchema>;

/**
 * Interface for transaction filter options
 */
export interface TransactionFilter {
  startDate?: Date;
  endDate?: Date;
  singleDate?: Date;
  type?: TransactionType;
  source?: TransactionSource;
  status?: TransactionStatus;
  location?: string;
  clientId?: string;
  staffId?: string;
  search?: string;
  minAmount?: number;
  maxAmount?: number;
}
