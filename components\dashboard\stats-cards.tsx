"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { DollarSign, Calendar, Users } from "lucide-react"
import { useAuth } from "@/lib/auth-provider"
import { useCurrency } from "@/lib/currency-provider"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { format, differenceInDays } from "date-fns"

interface StatsCardsProps {
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export function StatsCards({ dateRange }: StatsCardsProps) {
  const { currentLocation } = useAuth()
  const { currency } = useCurrency()
  const [revenueData, setRevenueData] = useState({
    total: 4875.25,
    card: 3256.75,
    cash: 1618.50,
    percentChange: 20.1
  })

  // Generate revenue data based on date range
  useEffect(() => {
    if (!dateRange) return

    // Generate random but consistent data based on the date range
    const daysDiff = differenceInDays(dateRange.to, dateRange.from) + 1
    const seed = dateRange.from.getTime() + (currentLocation === 'all' ? 0 : currentLocation.charCodeAt(0))
    const randomFactor = ((seed % 100) / 100) * 0.4 + 0.8 // 0.8 to 1.2

    // Base values that scale with the number of days
    const baseTotal = 500 * daysDiff * randomFactor
    const baseCard = baseTotal * 0.67 // 67% card payments
    const baseCash = baseTotal * 0.33 // 33% cash payments

    // Add some randomness but keep the proportions
    const total = Math.round(baseTotal * 100) / 100
    const card = Math.round(baseCard * 100) / 100
    const cash = Math.round(baseCash * 100) / 100

    // Calculate a fake percent change (positive or negative)
    const percentChange = Math.round((randomFactor - 1) * 100 * 2) / 1

    setRevenueData({
      total,
      card,
      cash,
      percentChange
    })
  }, [dateRange, currentLocation])

  // Format the date range for display
  const getDateRangeText = () => {
    if (!dateRange) return "from last month"

    const daysDiff = differenceInDays(dateRange.to, dateRange.from)
    if (daysDiff === 0) return "today"
    if (daysDiff === 1) return "yesterday"
    if (daysDiff < 7) return "this week"
    if (daysDiff < 31) return "this month"
    return "this period"
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Completed Revenue */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Completed Revenue</p>
              <h3 className="text-2xl font-bold mt-1">
                <CurrencyDisplay amount={revenueData.total} />
              </h3>
              <div className="flex flex-col gap-1 mt-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Card</span>
                  <span className="text-xs font-medium">
                    <CurrencyDisplay amount={revenueData.card} />
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Cash</span>
                  <span className="text-xs font-medium">
                    <CurrencyDisplay amount={revenueData.cash} />
                  </span>
                </div>
              </div>
              <p className={`text-xs ${revenueData.percentChange >= 0 ? 'text-green-600' : 'text-red-600'} mt-2`}>
                {revenueData.percentChange >= 0 ? '+' : ''}{revenueData.percentChange}% {getDateRangeText()}
              </p>
            </div>
            <div className="bg-muted p-2 rounded-md">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pending Revenue */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Pending Revenue</p>
              <h3 className="text-2xl font-bold mt-1">
                <CurrencyDisplay amount={revenueData.total * 0.25} />
              </h3>
              <p className="text-xs text-muted-foreground mt-1">Awaiting checkout</p>
            </div>
            <div className="bg-muted p-2 rounded-md">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Appointments */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Appointments</p>
              <h3 className="text-2xl font-bold mt-1">
                {dateRange ?
                  `+${Math.round(differenceInDays(dateRange.to, dateRange.from) * 15 + 10)}` :
                  "+573"}
              </h3>
              <p className={`text-xs ${revenueData.percentChange >= 0 ? 'text-green-600' : 'text-red-600'} mt-1`}>
                {revenueData.percentChange >= 0 ? '+' : ''}{Math.round(revenueData.percentChange * 0.9)}% {getDateRangeText()}
              </p>
            </div>
            <div className="bg-muted p-2 rounded-md">
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* New Clients */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground">New Clients</p>
              <h3 className="text-2xl font-bold mt-1">
                {dateRange ?
                  `+${Math.round(differenceInDays(dateRange.to, dateRange.from) * 3 + 5)}` :
                  "+89"}
              </h3>
              <p className={`text-xs ${revenueData.percentChange * 0.8 >= 0 ? 'text-green-600' : 'text-red-600'} mt-1`}>
                {revenueData.percentChange * 0.8 >= 0 ? '+' : ''}{Math.round(revenueData.percentChange * 0.8)}% {getDateRangeText()}
              </p>
            </div>
            <div className="bg-muted p-2 rounded-md">
              <Users className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

