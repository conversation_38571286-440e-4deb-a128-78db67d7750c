export interface LoyaltyReward {
  id: string;
  name: string;
  pointsCost: number;
  expiresAt: string;
}

export interface RedeemedReward {
  id: string;
  rewardId: string;
  name: string;
  redeemedAt: string;
  usedAt: string | null;
  pointsCost: number;
}

export interface PointHistoryItem {
  id: string;
  date: string;
  description: string;
  points: number;
  type: "earned" | "redeemed";
}

export interface LoyaltyData {
  points: number;
  pointsToNextReward: number;
  totalPointsEarned: number;
  memberSince: string;
  tier: string;
  nextTier: string;
  pointsToNextTier: number;
  availableRewards: LoyaltyReward[];
  redeemedRewards: RedeemedReward[];
}
