"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { MessageSquare, X, Minimize2, Maximize2 } from "lucide-react"
import { ChatMessage, type ChatMessageProps } from "./chat-message"
import { ChatInput } from "./chat-input"
import { cn } from "@/lib/utils"

// Mock initial messages
const initialMessages: ChatMessageProps[] = [
  {
    id: "1",
    content: "Just confirmed the color appointment with <PERSON> for tomorrow.",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    sender: {
      id: "staff1",
      name: "<PERSON>",
      location: "Downtown Salon",
      role: "Stylist",
    },
  },
  {
    id: "2",
    content: "<PERSON>'s highlights will take longer than expected. I've blocked an extra 30 minutes.",
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    sender: {
      id: "staff2",
      name: "<PERSON>",
      location: "Westside Salon",
      role: "Colorist",
    },
  },
  {
    id: "3",
    content: "We're running low on purple shampoo at Northside. Can someone bring some over?",
    timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
    sender: {
      id: "staff3",
      name: "Robert Taylor",
      location: "Northside Salon",
      role: "Manager",
    },
  },
]

// Mock current user
const currentUser = {
  id: "staff4",
  name: "Michael Chen",
  location: "Downtown Salon",
  role: "Colorist",
}

interface ActivityFeedProps {
  className?: string
}

export function ActivityFeed({ className }: ActivityFeedProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isMinimized, setIsMinimized] = useState(true)
  const [messages, setMessages] = useState<ChatMessageProps[]>(initialMessages)
  const [unreadCount, setUnreadCount] = useState(initialMessages.length)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Scroll to bottom when messages change or chat expands
  useEffect(() => {
    if (isExpanded && !isMinimized) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages, isExpanded, isMinimized])

  // Reset unread count when expanding
  useEffect(() => {
    if (isExpanded && !isMinimized) {
      setUnreadCount(0)
    }
  }, [isExpanded, isMinimized])

  const handleSendMessage = (content: string) => {
    const newMessage: ChatMessageProps = {
      id: `msg-${Date.now()}`,
      content,
      timestamp: new Date(),
      sender: currentUser,
      isCurrentUser: true,
    }
    setMessages([...messages, newMessage])
  }

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized)
    if (isMinimized) {
      setUnreadCount(0)
    }
  }

  const toggleExpand = () => {
    setIsExpanded(!isExpanded)
    if (!isExpanded) {
      setIsMinimized(false)
      setUnreadCount(0)
    }
  }

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50 flex flex-col",
        isExpanded ? "w-80 sm:w-96" : "w-64",
        isMinimized ? "h-auto" : isExpanded ? "h-[600px]" : "h-96",
        className,
      )}
    >
      <Card className="flex flex-col h-full shadow-lg border-primary/20">
        <CardHeader className="p-3 flex flex-row items-center justify-between space-y-0 bg-primary text-primary-foreground">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <h3 className="font-medium text-sm">Staff Activity Feed</h3>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="h-5 px-1">
                {unreadCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
              onClick={toggleExpand}
            >
              {isExpanded ? <Minimize2 className="h-3.5 w-3.5" /> : <Maximize2 className="h-3.5 w-3.5" />}
              <span className="sr-only">{isExpanded ? "Reduce" : "Expand"}</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
              onClick={toggleMinimize}
            >
              {isMinimized ? <MessageSquare className="h-3.5 w-3.5" /> : <X className="h-3.5 w-3.5" />}
              <span className="sr-only">{isMinimized ? "Open" : "Minimize"}</span>
            </Button>
          </div>
        </CardHeader>

        {!isMinimized && (
          <>
            <CardContent className="flex-1 p-3 overflow-hidden">
              <ScrollArea className="h-full pr-4">
                <div className="space-y-1">
                  {messages.map((message) => (
                    <ChatMessage key={message.id} {...message} isCurrentUser={message.sender.id === currentUser.id} />
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>
            </CardContent>
            <CardFooter className="p-0">
              <ChatInput onSendMessage={handleSendMessage} />
            </CardFooter>
          </>
        )}
      </Card>
    </div>
  )
}

